import { useContext } from 'react';
import { useChat as useChatContext } from '@/contexts/ChatContext';
import { UseChatReturn } from '@/types/chat';

/**
 * Custom hook for chat functionality
 * Provides a simplified interface to the chat context
 */
export const useChat = (): UseChatReturn => {
  return useChatContext();
};

/**
 * Hook for getting a specific chat by ID
 */
export const useChatById = (chatId: string | null) => {
  const { chats } = useChat();
  
  if (!chatId) return null;
  
  return chats.find(chat => chat.id === chatId) || null;
};

/**
 * Hook for getting unread message count
 */
export const useUnreadCount = () => {
  const { chats } = useChat();
  
  return chats.reduce((total, chat) => total + (chat.unread_count || 0), 0);
};

/**
 * Hook for checking if currently in a specific chat
 */
export const useIsInChat = (chatId: string | null) => {
  const { currentChat } = useChat();
  
  return currentChat?.id === chatId;
};

/**
 * Hook for getting the current chat's participants (excluding current user)
 */
export const useChatParticipants = () => {
  const { currentChat } = useChat();
  
  if (!currentChat) return [];
  
  // Filter out admin users if needed, or return all participants
  return currentChat.participants;
};

/**
 * Hook for getting connection status with retry functionality
 */
export const useConnectionStatus = () => {
  const { isConnected, reconnect, error } = useChat();
  
  return {
    isConnected,
    reconnect,
    hasError: !!error,
    error
  };
};
