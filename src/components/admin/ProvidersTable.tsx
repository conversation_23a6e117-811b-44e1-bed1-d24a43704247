
import React, { useState, useEffect } from 'react';
import useAuthHeader from 'react-auth-kit/hooks/useAuthHeader';
import { nullToUndefined } from '@/utils/typeHelpers';
import { providerService, Provider } from '@/services/providerService';
import { toast } from 'sonner';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { MoreHorizontal, MessageSquare, Trash2, Eye, Building2, Link, Link2Off } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import BusinessLinkingDialog from "./BusinessLinkingDialog";
import BusinessUnlinkingDialog from "./BusinessUnlinkingDialog";

interface ProviderStats {
  totalJobs: number;
  averageRating: number;
  revenue: number;
}

export const ProvidersTable: React.FC = () => {
  const [providers, setProviders] = useState<Provider[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [providerToDelete, setProviderToDelete] = useState<Provider | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedProvider, setSelectedProvider] = useState<Provider | null>(null);
  const [providerStats, setProviderStats] = useState<any | null>(null);
  const [isBusinessLinkingOpen, setIsBusinessLinkingOpen] = useState(false);
  const [isBusinessUnlinkingOpen, setIsBusinessUnlinkingOpen] = useState(false);
  const authHeader = useAuthHeader();

  // Fetch providers
  useEffect(() => {
    const fetchProviders = async () => {
      setIsLoading(true);
      try {
        const response = await providerService.getProviders(1, 10, undefined, nullToUndefined(authHeader) || '');
        
        if (response.isSuccess && response.data) {
          setProviders(response.data.data);
        } else {
          toast.error(response.error || 'Failed to fetch providers');
        }
      } catch (error) {
        console.error('Error fetching providers:', error);
        toast.error('Failed to fetch providers');
      } finally {
        setIsLoading(false);
      }
    };

    fetchProviders();
  }, [authHeader]);

  const fetchProviderStats = async (providerId: string) => {
    try {
      const response = await providerService.getProviderStats(providerId, nullToUndefined(authHeader) || '');
      
      if (response.isSuccess && response.data) {
        return response.data;
      }
      return null;
    } catch (error) {
      console.error('Error fetching provider stats:', error);
      return null;
    }
  };

  const handleViewDetails = async (provider: Provider) => {
    setSelectedProvider(provider);
    
    const stats = await fetchProviderStats(provider.id);
    if (stats) {
      setProviderStats(stats);
    }
    
    setIsDetailModalOpen(true);
  };

  // Handle provider deletion with proper type handling
  const handleDeleteProvider = async () => {
    if (!providerToDelete) return;

    setIsDeleting(true);
    try {
      const response = await providerService.deleteProvider(
        providerToDelete.id,
        nullToUndefined(authHeader) || ''
      );

      if (response.isSuccess) {
        toast.success('Provider deleted successfully');
        setProviders(providers.filter(p => p.id !== providerToDelete.id));
        setIsDeleteModalOpen(false);
        setProviderToDelete(null);
      } else {
        toast.error(response.error || 'Failed to delete provider');
      }
    } catch (error) {
      console.error('Error deleting provider:', error);
      toast.error('Failed to delete provider');
    } finally {
      setIsDeleting(false);
    }
  };

  if (isLoading) {
    return <div>Loading providers...</div>;
  }

  return (
    <>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Email</TableHead>
            <TableHead>Phone</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {providers.map((provider) => (
            <TableRow key={provider.id}>
              <TableCell>
                <div className="flex items-center gap-2">
                  <Avatar>
                    <AvatarImage src={provider.avatar || "/placeholder.svg"} alt={provider.name} />
                    <AvatarFallback>{provider.name.charAt(0)}</AvatarFallback>
                  </Avatar>
                  {provider.name}
                </div>
              </TableCell>
              <TableCell>{provider.email}</TableCell>
              <TableCell>{provider.phone}</TableCell>
              <TableCell>
                {provider.is_active ? (
                  <Badge variant="outline">Active</Badge>
                ) : (
                  <Badge>Inactive</Badge>
                )}
              </TableCell>
              <TableCell className="text-right">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <span className="sr-only">Open menu</span>
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => handleViewDetails(provider)}>
                      <Eye className="mr-2 h-4 w-4" /> View Details
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => {
                      setSelectedProvider(provider);
                      setIsBusinessLinkingOpen(true);
                    }}>
                      <Building2 className="mr-2 h-4 w-4" /> Link Business
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => {
                      setSelectedProvider(provider);
                      setIsBusinessUnlinkingOpen(true);
                    }}>
                      <Link2Off className="mr-2 h-4 w-4" /> Unlink Business
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <MessageSquare className="mr-2 h-4 w-4" /> Send Message
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => {
                        setProviderToDelete(provider);
                        setIsDeleteModalOpen(true);
                      }}
                    >
                      <Trash2 className="mr-2 h-4 w-4" /> Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {/* Provider Details Modal */}
      <Dialog open={isDetailModalOpen} onOpenChange={setIsDetailModalOpen}>
        <DialogContent className="sm:max-w-[625px]">
          <DialogHeader>
            <DialogTitle>Provider Details</DialogTitle>
            <DialogDescription>
              View detailed information about the selected provider.
            </DialogDescription>
          </DialogHeader>
          {selectedProvider && providerStats && (
            <ScrollArea className="h-[400px] w-full space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <div className="text-lg font-semibold">Personal Information</div>
                  <div className="mt-2 space-y-1">
                    <p><strong>Name:</strong> {selectedProvider.name}</p>
                    <p><strong>Email:</strong> {selectedProvider.email}</p>
                    <p><strong>Phone:</strong> {selectedProvider.phone}</p>
                    <p><strong>Location:</strong> {selectedProvider.location}</p>
                  </div>
                </div>
                <div>
                  <div className="text-lg font-semibold">Business Information</div>
                  <div className="mt-2 space-y-1">
                    <p><strong>Business Name:</strong> {selectedProvider.business_name || 'Not linked'}</p>
                    <p><strong>Category:</strong> {selectedProvider.category || 'N/A'}</p>
                    <p><strong>Specialization:</strong> {selectedProvider.specialty || 'N/A'}</p>
                    <div className="flex space-x-2 mt-2">
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => setIsBusinessLinkingOpen(true)}
                      >
                        <Link className="mr-2 h-4 w-4" /> Link Business
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => setIsBusinessUnlinkingOpen(true)}
                      >
                        <Link2Off className="mr-2 h-4 w-4" /> Unlink Business
                      </Button>
                    </div>
                  </div>
                </div>
              </div>

              <div className="border-t pt-4">
                <div className="text-lg font-semibold">Statistics</div>
                <div className="mt-2 space-y-1">
                  <p><strong>Total Jobs:</strong> {providerStats.totalJobs}</p>
                  <p><strong>Average Rating:</strong> {providerStats.averageRating}</p>
                  <p><strong>Revenue:</strong> ${providerStats.revenue}</p>
                </div>
              </div>
            </ScrollArea>
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteModalOpen} onOpenChange={setIsDeleteModalOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the provider.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              disabled={isDeleting}
              onClick={handleDeleteProvider}
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Business Linking Dialog */}
      <BusinessLinkingDialog
        isOpen={isBusinessLinkingOpen}
        onClose={() => setIsBusinessLinkingOpen(false)}
        provider={selectedProvider}
        onSuccess={() => {
          // Refresh provider details if needed
          if (selectedProvider) {
            handleViewDetails(selectedProvider);
          }
        }}
      />

      {/* Business Unlinking Dialog */}
      <BusinessUnlinkingDialog
        isOpen={isBusinessUnlinkingOpen}
        onClose={() => setIsBusinessUnlinkingOpen(false)}
        provider={selectedProvider}
        onSuccess={() => {
          // Refresh provider details if needed
          if (selectedProvider) {
            handleViewDetails(selectedProvider);
          }
        }}
      />
    </>
  );
};
