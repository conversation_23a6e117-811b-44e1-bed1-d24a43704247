import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/features/auth/hooks/useAuth";
import { providerService, Provider } from "@/services/providerService";
import { planService } from "@/services/planService";
import { ProviderTierManagementFormValues, providerTierManagementSchema, ProviderPlan } from "./schemas";
import { nullToUndefined } from "@/utils/typeHelpers";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Loader2 } from "lucide-react";



export const ProviderTierManagementForm = () => {
  const { toast } = useToast();
  const { token } = useAuth();
  const [providers, setProviders] = useState<Provider[]>([]);
  const [isLoadingProviders, setIsLoadingProviders] = useState(false);
  const [plans, setPlans] = useState<ProviderPlan[]>([]);
  const [isLoadingPlans, setIsLoadingPlans] = useState(false);
  const [selectedProvider, setSelectedProvider] = useState<Provider | null>(null);

  const form = useForm<ProviderTierManagementFormValues>({
    resolver: zodResolver(providerTierManagementSchema),
    defaultValues: {
      providerId: "",
      planId: "",
      duration: "monthly",
      notes: "",
    },
  });

  const { formState: { isSubmitting } } = form;



  // Fetch providers and plans when component mounts
  useEffect(() => {
    const fetchData = async () => {
      setIsLoadingProviders(true);
      setIsLoadingPlans(true);
      
      try {
        // Fetch providers
        const providersResponse = await providerService.getProviders(1, 100, undefined, nullToUndefined(token));
        if (providersResponse.isSuccess && providersResponse.data) {
          setProviders(providersResponse.data.data);
        } else {
          toast({
            title: "Error fetching providers",
            description: providersResponse.error || "Failed to load providers",
            variant: "destructive",
          });
        }
        
        // Fetch plans
        const plansResponse = await planService.getPlans(1, 100, nullToUndefined(token));
        if (plansResponse.success && plansResponse.data) {
          setPlans(plansResponse.data.data);
        } else {
          toast({
            title: "Error fetching plans",
            description: plansResponse.message || "Failed to load subscription plans",
            variant: "destructive",
          });
        }
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to load data. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoadingProviders(false);
        setIsLoadingPlans(false);
      }
    };

    fetchData();
  }, [token, toast]);

  // Update selected provider when providerId changes
  useEffect(() => {
    const providerId = form.watch("providerId");
    
    if (providerId) {
      const provider = providers.find(p => String(p.id) === providerId);
      setSelectedProvider(provider || null);
    } else {
      setSelectedProvider(null);
    }
  }, [form.watch("providerId"), providers]);

  const onSubmit = async (data: ProviderTierManagementFormValues) => {
    try {
      const response = await planService.assignPlanToProvider(
        data.providerId,
        data.planId,
        data.notes || undefined,
        nullToUndefined(token),
        data.duration
      );
      
      if (response.success) {
        // Get plan and provider details for the success message
        const plan = plans.find(p => p.id === data.planId);
        const provider = providers.find(p => String(p.id) === data.providerId);
        
        toast({
          title: "Tier updated",
          description: `${provider?.name} has been assigned to the ${plan?.name} plan.`,
        });
        
        // Reset the form
        form.reset();
        setSelectedProvider(null);
      } else {
        toast({
          title: "Update failed",
          description: response.message || "There was an error updating the provider tier.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Update failed",
        description: "There was an error updating the provider tier. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Provider Tier Management</CardTitle>
        <CardDescription>
          Assign subscription plans to providers and manage their tier status.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="providerId"
              render={({ field }) => {
                return (
                  <FormItem>
                    <FormLabel>Provider</FormLabel>
                    <Select
                      onValueChange={(value) => {
                        field.onChange(value);
                      }}
                      value={field.value}
                      disabled={isLoadingProviders}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a provider" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {isLoadingProviders ? (
                          <div className="flex items-center justify-center p-4">
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            <span>Loading providers...</span>
                          </div>
                        ) : (
                          providers.map((provider) => (
                            <SelectItem key={provider.id} value={String(provider.id)}>
                              {provider.name} {provider.business_name ? `(${provider.business_name})` : ''}
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                );
              }}
            />

            {selectedProvider && (
              <div className="bg-muted p-4 rounded-md">
                <h3 className="font-medium mb-2">Provider Details</h3>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <span className="text-muted-foreground">Email:</span> {selectedProvider.email}
                  </div>
                  <div>
                    <span className="text-muted-foreground">Phone:</span> {selectedProvider.phone || 'N/A'}
                  </div>
                  <div>
                    <span className="text-muted-foreground">Business:</span> {selectedProvider.business_name || 'N/A'}
                  </div>
                  <div>
                    <span className="text-muted-foreground">Current Plan:</span> {selectedProvider.plan || 'None'}
                  </div>
                </div>
              </div>
            )}

            <FormField
              control={form.control}
              name="planId"
              render={({ field }) => {
                return (
                  <FormItem>
                    <FormLabel>Subscription Plan</FormLabel>
                    <Select
                      onValueChange={(value) => {
                        field.onChange(value);
                      }}
                      value={field.value}
                      disabled={isLoadingPlans}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a plan" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {isLoadingPlans ? (
                          <div className="flex items-center justify-center p-4">
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            <span>Loading plans...</span>
                          </div>
                        ) : (
                          plans.map((plan) => (
                              <SelectItem key={plan.id} value={String(plan.id)}>
                                {plan.name} (${plan.price}{plan.price === 0 ? '' : '/month'}) - {plan.commission} commission
                              </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                );
              }}
            />

            <FormField
              control={form.control}
              name="duration"
              render={({ field }) => {
                return (
                  <FormItem>
                    <FormLabel>Duration</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select duration" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="monthly">Monthly</SelectItem>
                        <SelectItem value="yearly">Yearly</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                );
              }}
            />



            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => {
                return (
                  <FormItem>
                    <FormLabel>Notes (Optional - Max 500 characters)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Add any notes about this tier assignment"
                        className="resize-none"
                        maxLength={500}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                );
              }}
            />

            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                "Assign Plan"
              )}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};