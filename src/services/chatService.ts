import { ApiResponse, apiService } from './api';
import { 
  Chat, 
  ChatListResponse, 
  Message, 
  MessagesResponse, 
  SendMessageRequest, 
  SendMessageResponse 
} from '@/types/chat';

/**
 * Chat Service
 * Handles all chat-related API operations using the existing apiService
 */
export const chatService = {
  /**
   * Get list of chats for the current user
   * @param token - Authorization token
   * @returns Promise with chat list response
   */
  getChats: async (token?: string): Promise<ApiResponse<ChatListResponse>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    return apiService<ChatListResponse>('/api/chats', {
      method: 'GET',
      headers,
      requiresAuth: true,
    });
  },

  /**
   * Get messages for a specific chat
   * @param chatId - Chat ID
   * @param page - Page number for pagination (optional)
   * @param token - Authorization token
   * @returns Promise with messages response
   */
  getMessages: async (
    chatId: string, 
    page: number = 1, 
    token?: string
  ): Promise<ApiResponse<MessagesResponse>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    let endpoint = `/api/chats/${chatId}/messages`;
    if (page > 1) {
      endpoint += `?page=${page}`;
    }

    return apiService<MessagesResponse>(endpoint, {
      method: 'GET',
      headers,
      requiresAuth: true,
    });
  },

  /**
   * Send a message to a chat
   * @param chatId - Chat ID
   * @param messageData - Message content and type
   * @param token - Authorization token
   * @returns Promise with sent message response
   */
  sendMessage: async (
    chatId: string, 
    messageData: SendMessageRequest, 
    token?: string
  ): Promise<ApiResponse<SendMessageResponse>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    return apiService<SendMessageResponse>(`/api/chats/${chatId}/messages`, {
      method: 'POST',
      headers,
      body: messageData,
      requiresAuth: true,
    });
  },

  /**
   * Mark all messages in a chat as read
   * @param chatId - Chat ID
   * @param token - Authorization token
   * @returns Promise with success response
   */
  markAsRead: async (
    chatId: string, 
    token?: string
  ): Promise<ApiResponse<{ success: boolean; message?: string }>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    return apiService<{ success: boolean; message?: string }>(`/api/chats/${chatId}/read`, {
      method: 'POST',
      headers,
      requiresAuth: true,
    });
  },

  /**
   * Delete a specific message
   * @param chatId - Chat ID
   * @param messageId - Message ID
   * @param token - Authorization token
   * @returns Promise with success response
   */
  deleteMessage: async (
    chatId: string, 
    messageId: string, 
    token?: string
  ): Promise<ApiResponse<{ success: boolean; message?: string }>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    return apiService<{ success: boolean; message?: string }>(
      `/api/chats/${chatId}/messages/${messageId}`, 
      {
        method: 'DELETE',
        headers,
        requiresAuth: true,
      }
    );
  },

  /**
   * Create a new chat (if needed for admin functionality)
   * @param participantIds - Array of user IDs to include in chat
   * @param type - Chat type ('direct' or 'group')
   * @param name - Chat name (optional, for group chats)
   * @param token - Authorization token
   * @returns Promise with created chat response
   */
  createChat: async (
    participantIds: string[], 
    type: 'direct' | 'group' = 'direct',
    name?: string,
    token?: string
  ): Promise<ApiResponse<{ data: Chat; success: boolean; message?: string }>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    const body = {
      participant_ids: participantIds,
      type,
      ...(name && { name })
    };

    return apiService<{ data: Chat; success: boolean; message?: string }>('/api/chats', {
      method: 'POST',
      headers,
      body,
      requiresAuth: true,
    });
  },
};
